name: Artifact Build

on: [pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'adopt'
    - name: <PERSON> execute permission for gradlew
      run: chmod +x gradlew
    - name: Run Gradle
      run: ./gradlew assemblePrereleaseDebug
    - name: Upload Artifact
      uses: actions/upload-artifact@v4
      with:
        name: pull-request-build
        path: "app/build/outputs/apk/prerelease/debug/*.apk"
