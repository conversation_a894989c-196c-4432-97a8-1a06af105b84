name: Pre-release

on:
  push:
    branches: [ master ]
    paths-ignore:
      - '*.md'
      - '*.json'
      - '**/wcokey.txt'

concurrency: 
  group: "pre-release"
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Generate access token
      id: generate_token
      uses: tibdex/github-app-token@v2
      with:
        app_id: ${{ secrets.GH_APP_ID }}
        private_key: ${{ secrets.GH_APP_KEY }}
        repository: "recloudstream/secrets"
    - uses: actions/checkout@v4
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'adopt'
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    - name: Fetch keystore
      id: fetch_keystore
      run: |
        TMP_KEYSTORE_FILE_PATH="${RUNNER_TEMP}"/keystore
        mkdir -p "${TMP_KEYSTORE_FILE_PATH}"
        curl -H "Authorization: token ${{ steps.generate_token.outputs.token }}" -o "${TMP_KEYSTORE_FILE_PATH}/prerelease_keystore.keystore" "https://raw.githubusercontent.com/recloudstream/secrets/master/keystore.jks"
        curl -H "Authorization: token ${{ steps.generate_token.outputs.token }}" -o "keystore_password.txt" "https://raw.githubusercontent.com/recloudstream/secrets/master/keystore_password.txt"
        KEY_PWD="$(cat keystore_password.txt)"
        echo "::add-mask::${KEY_PWD}"
        echo "key_pwd=$KEY_PWD" >> $GITHUB_OUTPUT
    - name: Run Gradle
      run: |
        ./gradlew assemblePrerelease build androidSourcesJar
        ./gradlew makeJar # for classes.jar, has to be done after assemblePrerelease
      env:
        SIGNING_KEY_ALIAS: "key0"
        SIGNING_KEY_PASSWORD: ${{ steps.fetch_keystore.outputs.key_pwd }}
        SIGNING_STORE_PASSWORD: ${{ steps.fetch_keystore.outputs.key_pwd }}
        SIMKL_CLIENT_ID: ${{ secrets.SIMKL_CLIENT_ID }}
        SIMKL_CLIENT_SECRET: ${{ secrets.SIMKL_CLIENT_SECRET }}
        MDL_API_KEY: ${{ secrets.MDL_API_KEY }}
    - name: Create pre-release
      uses: "marvinpinto/action-automatic-releases@latest"
      with:
        repo_token: "${{ secrets.GITHUB_TOKEN }}"
        automatic_release_tag: "pre-release"
        prerelease: true
        title: "Pre-release Build"
        files: |
          app/build/outputs/apk/prerelease/release/*.apk
          app/build/libs/app-sources.jar
          app/build/classes.jar
