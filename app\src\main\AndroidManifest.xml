<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk tools:overrideLibrary="go.torrServer.gojni" /> <!-- torrServer has a different api level -->

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- I dont remember, probs has to do with downloads -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- unless you only use cs3 as a player for downloaded stuff, you need this -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Downloads -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Downloads on low api devices -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" /> <!-- Plugin API -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" /> <!-- Used for player vertical slide -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- Used for app update -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Used for app notifications on Android 13+ -->
    <uses-permission android:name="com.android.providers.tv.permission.WRITE_EPG_DATA" /> <!-- Used for Android TV watch next -->
    <uses-permission android:name="android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION" /> <!-- Used for updates without prompt -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Used for update service -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- We can use this directly as CS3 is not on Play Store -->

    <!-- Required for OpenInAppAction and getting arbitrary Aniyomi packages  -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <!-- Fixes android tv fuckery -->
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <!-- Without the large heap Exoplayer buffering gets reset due to OOM. -->
    <!--TODO https://stackoverflow.com/questions/41799732/chromecast-button-not-visible-in-android-->
    <application
        android:name=".AcraApplication"
        android:allowBackup="true"
        android:enableOnBackInvokedCallback="true"
        android:appCategory="video"
        android:banner="@mipmap/ic_banner"
        android:fullBackupContent="@xml/backup_descriptor"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:targetApi="35">

        <meta-data
            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
            android:value="com.lagradost.cloudstream3.utils.CastOptionsProvider" />

        <profileable
            android:shell="true"
            tools:targetApi="q" />

        <activity
            android:name=".ui.player.DownloadedPlayerActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboard|keyboardHidden|navigation"
            android:exported="true"
            android:resizeableActivity="true"
            android:screenOrientation="userLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="com.lagradost.cloudstream3.downloadedplayer"
            android:launchMode="singleTask"
            tools:ignore="DiscouragedApi">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="content" />
                <data android:mimeType="video/*" />
            </intent-filter>

            <!-- I dont think this label can be translated, but idk -->
            <intent-filter android:label="@string/play_with_app_name">
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.SEND" />
                <action android:name="android.intent.action.OPEN_DOCUMENT" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="magnet" />
            </intent-filter>
            <!--<intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.SEND" />
                <action android:name="android.intent.action.OPEN_DOCUMENT" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="application/x-bittorrent" />
            </intent-filter>-->
        </activity>
        <!--
        android:launchMode="singleTask"
        is a bit experimental, it makes loading repositories from browser still stay on the same page
        no idea about side effects
        -->
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboard|keyboardHidden|navigation|uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:supportsPictureInPicture="true">

            <!-- openstreamplayer://encodedUrl?name=Dune -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="openstreamplayer" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="openstreamapp" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="openstreamrepo" />
            </intent-filter>

            <!-- Allow searching with intents: openstreamsearch://Your%20Name -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="openstreamsearch" />
            </intent-filter>

            <!--
            Allow opening from continue watching with intents: openstreamsearch://1234
            Used on Android TV Watch Next
             -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="openstreamcontinuewatching" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="cs.repo"
                    android:pathPrefix="/"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.account.AccountSelectActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboard|keyboardHidden"
            android:exported="true">
            <intent-filter android:exported="true">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".receivers.VideoDownloadRestartReceiver"
            android:enabled="false"
            android:exported="false">
            <intent-filter android:exported="false">
                <action android:name="restart_service" />
            </intent-filter>
        </receiver>

        <service
            android:name=".services.VideoDownloadService"
            android:enabled="true"
            android:foregroundServiceType="dataSync"
            android:exported="false" />

        <!-- Necessary for WorkManager services: https://stackoverflow.com/a/77186316 -->
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:foregroundServiceType="dataSync"
            tools:node="merge" />

        <activity
            android:name=".ui.ControllerActivity"
            android:exported="false" />

        <service
            android:name=".services.PackageInstallerService"
            android:foregroundServiceType="dataSync"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:enabled="true"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
    </application>
    
</manifest>
