name: Archive build

on:
  push:
    branches: [ master ]
    paths-ignore:
      - '*.md'
      - '*.json'
      - '**/wcokey.txt'
  workflow_dispatch:

concurrency: 
  group: "Archive-build"
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Generate access token
      id: generate_token
      uses: tibdex/github-app-token@v2
      with:
        app_id: ${{ secrets.GH_APP_ID }}
        private_key: ${{ secrets.GH_APP_KEY }}
        repository: "recloudstream/secrets"
    - name: Generate access token (archive)
      id: generate_archive_token
      uses: tibdex/github-app-token@v2
      with:
        app_id: ${{ secrets.GH_APP_ID }}
        private_key: ${{ secrets.GH_APP_KEY }}
        repository: "recloudstream/cloudstream-archive"
    - uses: actions/checkout@v4
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'adopt'
    - name: <PERSON> execute permission for gradlew
      run: chmod +x gradlew
    - name: Fetch keystore
      id: fetch_keystore
      run: |
        TMP_KEYSTORE_FILE_PATH="${RUNNER_TEMP}"/keystore
        mkdir -p "${TMP_KEYSTORE_FILE_PATH}"
        curl -H "Authorization: token ${{ steps.generate_token.outputs.token }}" -o "${TMP_KEYSTORE_FILE_PATH}/prerelease_keystore.keystore" "https://raw.githubusercontent.com/recloudstream/secrets/master/keystore.jks"
        curl -H "Authorization: token ${{ steps.generate_token.outputs.token }}" -o "keystore_password.txt" "https://raw.githubusercontent.com/recloudstream/secrets/master/keystore_password.txt"
        KEY_PWD="$(cat keystore_password.txt)"
        echo "::add-mask::${KEY_PWD}"
        echo "key_pwd=$KEY_PWD" >> $GITHUB_OUTPUT
    - name: Run Gradle
      run: |
        ./gradlew assemblePrerelease
      env:
        SIGNING_KEY_ALIAS: "key0"
        SIGNING_KEY_PASSWORD: ${{ steps.fetch_keystore.outputs.key_pwd }}
        SIGNING_STORE_PASSWORD: ${{ steps.fetch_keystore.outputs.key_pwd }}
        SIMKL_CLIENT_ID: ${{ secrets.SIMKL_CLIENT_ID }}
        SIMKL_CLIENT_SECRET: ${{ secrets.SIMKL_CLIENT_SECRET }}
    - uses: actions/checkout@v4
      with:
        repository: "recloudstream/cloudstream-archive"
        token: ${{ steps.generate_archive_token.outputs.token }}
        path: "archive"

    - name: Move build
      run: |
        cp app/build/outputs/apk/prerelease/release/*.apk "archive/$(git rev-parse --short HEAD).apk"
    
    - name: Push archive
      run: |
        cd $GITHUB_WORKSPACE/archive
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Actions"
        git add .
        git commit --amend -m "Build $GITHUB_SHA" || exit 0   # do not error if nothing to commit
        git push --force