name: Issue automatic actions

on:
  issues:
    types: [opened]

jobs:
  issue-moderator:
    runs-on: ubuntu-latest
    steps:
      - name: Generate access token
        id: generate_token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.GH_APP_ID }}
          private_key: ${{ secrets.GH_APP_KEY }}
      - name: Similarity analysis
        id: similarity
        uses: actions-cool/issues-similarity-analysis@v1
        with:
          token: ${{ steps.generate_token.outputs.token }}
          filter-threshold: 0.60
          title-excludes: ''
          comment-title: |
            ### Your issue looks similar to these issues:
            Please close if duplicate.
          comment-body: '${index}. ${similarity} #${number}'
      - name: Label if possible duplicate
        if: steps.similarity.outputs.similar-issues-found =='true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ steps.generate_token.outputs.token }}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ["possible duplicate"]
            })
      - uses: actions/checkout@v4
      - name: Automatically close issues that dont follow the issue template
        uses: lucasbento/auto-close-issues@v1.0.2
        with:
          github-token: ${{ steps.generate_token.outputs.token }}
          issue-close-message: |
            @${issue.user.login}: hello! :wave:
            This issue is being automatically closed because it does not follow the issue template." 
          closed-issues-label: "invalid"
      - name: Check if issue mentions a provider
        id: provider_check
        env:
          GH_TEXT: "${{ github.event.issue.title }} ${{ github.event.issue.body }}"
        run: |
          wget --output-document check_issue.py "https://raw.githubusercontent.com/recloudstream/.github/master/.github/check_issue.py"
          pip3 install httpx
          RES="$(python3 ./check_issue.py)"
          echo "name=${RES}" >> $GITHUB_OUTPUT
      - name: Comment if issue mentions a provider
        if: steps.provider_check.outputs.name != 'none'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ steps.generate_token.outputs.token }}
          body: |
            Hello ${{ github.event.issue.user.login }}. 
            Please do not report any provider bugs here. This repository does not contain any providers. Please find the appropriate repository and report your issue there or join the [discord](https://discord.gg/5Hus6fM).
            
            Found provider name: `${{ steps.provider_check.outputs.name }}`
      - name: Label if mentions provider
        if: steps.provider_check.outputs.name != 'none'
        uses: actions/github-script@v7
        with:
          github-token: ${{ steps.generate_token.outputs.token }}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ["possible provider issue"]
            })
      - name: Add eyes reaction to all issues
        uses: actions-cool/emoji-helper@v1.0.0
        with:
          type: 'issue'
          token: ${{ steps.generate_token.outputs.token }}
          emoji: 'eyes'
          
          
