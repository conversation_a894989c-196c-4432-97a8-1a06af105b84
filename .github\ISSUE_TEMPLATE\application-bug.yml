name: 🐞 Application Issue Report
description: Report a issue in CloudStream
labels: [bug]
body:

  - type: textarea
    id: reproduce-steps
    attributes:
      label: Steps to reproduce
      description: Provide an example of the issue.
      placeholder: |
        Example:
          1. First step
          2. Second step
          3. Issue here
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      placeholder: |
        Example:
          "This should happen..."
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual behavior
      placeholder: |
        Example:
          "This happened instead..."
    validations:
      required: true

  - type: input
    id: cloudstream-version
    attributes:
      label: Cloudstream version and commit hash
      description: |
        You can find your Cloudstream version in **Settings**. Commit hash is the 7 character string next to the version.
      placeholder: |
        Example: "2.8.16 a49f466"
    validations:
      required: true

  - type: input
    id: android-version
    attributes:
      label: Android version
      description: |
        You can find this somewhere in your Android settings.
      placeholder: |
        Example: "Android 12"
    validations:
      required: true
   
  - type: textarea
    id: logcat
    attributes:
      label: Logcat
      placeholder: |
        To get logcat please go to Settings > Updates and backup > Show logcat 🐈.
        You can attach a file or link to some pastebin service if the file is too big.
      render: java

  - type: textarea
    id: other-details
    attributes:
      label: Other details
      placeholder: |
        Additional details and attachments.

  - type: checkboxes
    id: acknowledgements
    attributes:
      label: Acknowledgements
      description: Your issue will be closed if you haven't done these steps.
      options:
        - label: I am sure my issue is related to the app and **NOT some extension**.
          required: true
        - label: I have searched the existing issues and this is a new ticket, **NOT** a duplicate or related to another open issue.
          required: true
        - label: I have written a short but informative title.
          required: true
        - label: I have updated the app to pre-release version **[Latest](https://github.com/recloudstream/cloudstream/releases)**.
          required: true
        - label: I will fill out all of the requested information in this form.
          required: true
