name: ⭐ Feature request
description: Suggest a feature to improve the app
labels: [enhancement]
body:

  - type: textarea
    id: feature-description
    attributes:
      label: Describe your suggested feature
      description: How can an existing source be improved?
      placeholder: |
        Example:
          "It should work like this..."
    validations:
      required: true

  - type: textarea
    id: other-details
    attributes:
      label: Other details
      placeholder: |
        Additional details and attachments.

  - type: checkboxes
    id: acknowledgements
    attributes:
      label: Acknowledgements
      description: Your issue will be closed if you haven't done these steps.
      options:
        - label: My suggestion is **NOT** about adding a new provider
          required: true
        - label: I have searched the existing issues and this is a new ticket, **NOT** a duplicate or related to another open issue.
          required: true